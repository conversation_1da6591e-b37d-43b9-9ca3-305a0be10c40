/* 五彩斑斓的黑 - 2048游戏样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #000000, #1a1a1a, #000000);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* 背景粒子效果 */
.background-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 255, 198, 0.3) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* 游戏标题 */
.game-header {
    text-align: center;
    margin-bottom: 30px;
}

.game-title {
    margin-bottom: 20px;
}

.rainbow-text {
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(45deg, 
        #ff0000, #ff7f00, #ffff00, #00ff00, 
        #0000ff, #4b0082, #9400d3, #ff0000);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbow 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
}

@keyframes rainbow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.subtitle {
    display: block;
    font-size: 1.5rem;
    color: #888;
    margin-top: 10px;
}

/* 分数容器 */
.score-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.score-box {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 15px 25px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.score-label {
    font-size: 0.9rem;
    color: #ccc;
    margin-bottom: 5px;
}

.score-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* 游戏控制按钮 */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.control-btn {
    background: linear-gradient(45deg, #333, #555);
    border: none;
    border-radius: 10px;
    color: #fff;
    padding: 12px 20px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.control-btn:hover {
    background: linear-gradient(45deg, #555, #777);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* 游戏容器 */
.game-container {
    position: relative;
    margin: 0 auto;
    width: 500px;
    height: 500px;
}

.game-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 10px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 
        0 0 50px rgba(0, 0, 0, 0.8),
        inset 0 0 50px rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.grid-cell {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.grid-cell::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.grid-cell:hover::before {
    transform: translateX(100%);
}

/* 数字方块样式 */
.tile {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 900;
    border-radius: 10px;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 不同数值的彩色样式 */
.tile-2 { background: linear-gradient(45deg, #ff6b6b, #ee5a24); color: #fff; }
.tile-4 { background: linear-gradient(45deg, #feca57, #ff9ff3); color: #fff; }
.tile-8 { background: linear-gradient(45deg, #48dbfb, #0abde3); color: #fff; }
.tile-16 { background: linear-gradient(45deg, #1dd1a1, #10ac84); color: #fff; }
.tile-32 { background: linear-gradient(45deg, #a55eea, #8b5cf6); color: #fff; }
.tile-64 { background: linear-gradient(45deg, #fd79a8, #e84393); color: #fff; }
.tile-128 { background: linear-gradient(45deg, #fdcb6e, #e17055); color: #fff; font-size: 1.8rem; }
.tile-256 { background: linear-gradient(45deg, #6c5ce7, #a29bfe); color: #fff; font-size: 1.8rem; }
.tile-512 { background: linear-gradient(45deg, #fd79a8, #fdcb6e); color: #fff; font-size: 1.8rem; }
.tile-1024 { background: linear-gradient(45deg, #00b894, #00cec9); color: #fff; font-size: 1.5rem; }
.tile-2048 { 
    background: linear-gradient(45deg, #ff7675, #74b9ff, #a29bfe, #fd79a8);
    background-size: 400% 400%;
    animation: rainbow 2s ease-in-out infinite;
    color: #fff; 
    font-size: 1.5rem;
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
}

/* 游戏说明 */
.game-instructions {
    text-align: center;
    margin-top: 30px;
    color: #ccc;
}

.game-instructions h3 {
    margin-bottom: 10px;
    color: #fff;
}

.key-hints {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.key {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
}

/* 游戏覆盖层 */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10;
}

.game-overlay.show {
    opacity: 1;
    visibility: visible;
}

.overlay-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.overlay-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.overlay-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #ccc;
}

.overlay-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 15px;
    color: #fff;
    padding: 15px 30px;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.overlay-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

/* 动画效果 */
.tile-new {
    animation: tile-appear 0.3s ease;
}

.tile-merged {
    animation: tile-merge 0.3s ease;
}

@keyframes tile-appear {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes tile-merge {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* 粒子效果容器 */
.effects-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
}

#particles-canvas {
    width: 100%;
    height: 100%;
}

/* 移动动画 */
.tile-move {
    transition: all 0.15s ease-in-out;
}

/* 高分数字特效 */
.tile-1024, .tile-2048 {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    }
    to {
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.8), 0 0 40px rgba(255, 255, 255, 0.6);
    }
}

/* 响应式设计 */
@media (max-width: 600px) {
    .game-container {
        width: 90vw;
        height: 90vw;
        max-width: 400px;
        max-height: 400px;
    }

    .rainbow-text {
        font-size: 2rem;
    }

    .score-container {
        flex-direction: column;
        align-items: center;
    }

    .game-controls {
        flex-wrap: wrap;
    }

    .control-btn {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .grid-cell {
        font-size: 1.5rem;
    }

    .tile-128, .tile-256, .tile-512 {
        font-size: 1.3rem;
    }

    .tile-1024, .tile-2048 {
        font-size: 1.1rem;
    }
}
