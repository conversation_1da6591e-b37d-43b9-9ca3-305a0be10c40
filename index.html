<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五彩斑斓的黑 - 高级2048</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="background-particles"></div>
    
    <div class="container">
        <header class="game-header">
            <h1 class="game-title">
                <span class="rainbow-text">五彩斑斓的黑</span>
                <span class="subtitle">2048</span>
            </h1>
            
            <div class="score-container">
                <div class="score-box">
                    <div class="score-label">分数</div>
                    <div class="score-value" id="score">0</div>
                </div>
                <div class="score-box">
                    <div class="score-label">最高分</div>
                    <div class="score-value" id="best-score">0</div>
                </div>
            </div>
        </header>

        <div class="game-controls">
            <button class="control-btn" id="new-game-btn">新游戏</button>
            <button class="control-btn" id="undo-btn">撤销</button>
            <button class="control-btn" id="auto-play-btn">自动游戏</button>
        </div>

        <div class="game-container">
            <div class="game-grid" id="game-grid">
                <!-- 游戏网格将通过JavaScript生成 -->
            </div>
            
            <div class="game-overlay" id="game-overlay">
                <div class="overlay-content">
                    <h2 id="overlay-title">游戏结束</h2>
                    <p id="overlay-message">再试一次？</p>
                    <button class="overlay-btn" id="restart-btn">重新开始</button>
                </div>
            </div>
        </div>

        <div class="game-instructions">
            <h3>游戏说明</h3>
            <p>使用方向键移动方块，相同数字的方块会合并！</p>
            <div class="key-hints">
                <span class="key">↑</span>
                <span class="key">↓</span>
                <span class="key">←</span>
                <span class="key">→</span>
            </div>
        </div>

        <div class="effects-container">
            <canvas id="particles-canvas"></canvas>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
