// 五彩斑斓的黑 - 2048游戏逻辑

class Game2048 {
    constructor() {
        this.grid = [];
        this.score = 0;
        this.bestScore = parseInt(localStorage.getItem('bestScore')) || 0;
        this.previousState = null;
        this.isAutoPlaying = false;
        this.autoPlayInterval = null;
        
        this.initializeGrid();
        this.setupEventListeners();
        this.setupParticles();
        this.updateDisplay();
        this.addRandomTile();
        this.addRandomTile();
        this.render();
    }

    initializeGrid() {
        this.grid = Array(4).fill().map(() => Array(4).fill(0));
    }

    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.isAutoPlaying) return;
            
            switch(e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    this.move('up');
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.move('down');
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.move('left');
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.move('right');
                    break;
            }
        });

        // 触摸事件
        let startX, startY;
        const gameGrid = document.getElementById('game-grid');
        
        gameGrid.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        gameGrid.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            if (Math.abs(diffX) > Math.abs(diffY)) {
                if (diffX > 0) {
                    this.move('left');
                } else {
                    this.move('right');
                }
            } else {
                if (diffY > 0) {
                    this.move('up');
                } else {
                    this.move('down');
                }
            }
            
            startX = null;
            startY = null;
        });

        // 按钮事件
        document.getElementById('new-game-btn').addEventListener('click', () => {
            this.newGame();
        });

        document.getElementById('undo-btn').addEventListener('click', () => {
            this.undo();
        });

        document.getElementById('auto-play-btn').addEventListener('click', () => {
            this.toggleAutoPlay();
        });

        document.getElementById('restart-btn').addEventListener('click', () => {
            this.newGame();
        });
    }

    setupParticles() {
        this.canvas = document.getElementById('particles-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.particles = [];
        
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    saveState() {
        this.previousState = {
            grid: this.grid.map(row => [...row]),
            score: this.score
        };
    }

    undo() {
        if (this.previousState) {
            this.grid = this.previousState.grid;
            this.score = this.previousState.score;
            this.updateDisplay();
            this.render();
            this.previousState = null;
        }
    }

    newGame() {
        this.stopAutoPlay();
        this.initializeGrid();
        this.score = 0;
        this.previousState = null;
        this.hideOverlay();
        this.updateDisplay();
        this.addRandomTile();
        this.addRandomTile();
        this.render();
    }

    addRandomTile() {
        const emptyCells = [];
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (this.grid[i][j] === 0) {
                    emptyCells.push({row: i, col: j});
                }
            }
        }

        if (emptyCells.length > 0) {
            const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
            this.grid[randomCell.row][randomCell.col] = Math.random() < 0.9 ? 2 : 4;
            
            // 添加出现动画
            setTimeout(() => {
                const tile = this.getTileElement(randomCell.row, randomCell.col);
                if (tile) {
                    tile.classList.add('tile-new');
                    setTimeout(() => tile.classList.remove('tile-new'), 300);
                }
            }, 50);
        }
    }

    move(direction) {
        this.saveState();
        let moved = false;
        const newGrid = this.grid.map(row => [...row]);

        switch(direction) {
            case 'left':
                moved = this.moveLeft(newGrid);
                break;
            case 'right':
                moved = this.moveRight(newGrid);
                break;
            case 'up':
                moved = this.moveUp(newGrid);
                break;
            case 'down':
                moved = this.moveDown(newGrid);
                break;
        }

        if (moved) {
            this.grid = newGrid;
            this.addRandomTile();
            this.updateDisplay();
            this.render();
            this.checkGameState();
        }
    }

    moveLeft(grid) {
        let moved = false;
        for (let i = 0; i < 4; i++) {
            const row = grid[i].filter(val => val !== 0);
            for (let j = 0; j < row.length - 1; j++) {
                if (row[j] === row[j + 1]) {
                    row[j] *= 2;
                    this.score += row[j];
                    row.splice(j + 1, 1);
                    this.createMergeEffect(i, j, row[j]);
                }
            }
            while (row.length < 4) {
                row.push(0);
            }
            
            for (let j = 0; j < 4; j++) {
                if (grid[i][j] !== row[j]) {
                    moved = true;
                }
            }
            grid[i] = row;
        }
        return moved;
    }

    moveRight(grid) {
        let moved = false;
        for (let i = 0; i < 4; i++) {
            const row = grid[i].filter(val => val !== 0);
            for (let j = row.length - 1; j > 0; j--) {
                if (row[j] === row[j - 1]) {
                    row[j] *= 2;
                    this.score += row[j];
                    row.splice(j - 1, 1);
                    this.createMergeEffect(i, 4 - row.length + j - 1, row[j - 1] || row[j]);
                }
            }
            while (row.length < 4) {
                row.unshift(0);
            }
            
            for (let j = 0; j < 4; j++) {
                if (grid[i][j] !== row[j]) {
                    moved = true;
                }
            }
            grid[i] = row;
        }
        return moved;
    }

    moveUp(grid) {
        let moved = false;
        for (let j = 0; j < 4; j++) {
            const column = [];
            for (let i = 0; i < 4; i++) {
                if (grid[i][j] !== 0) {
                    column.push(grid[i][j]);
                }
            }
            
            for (let i = 0; i < column.length - 1; i++) {
                if (column[i] === column[i + 1]) {
                    column[i] *= 2;
                    this.score += column[i];
                    column.splice(i + 1, 1);
                    this.createMergeEffect(i, j, column[i]);
                }
            }
            
            while (column.length < 4) {
                column.push(0);
            }
            
            for (let i = 0; i < 4; i++) {
                if (grid[i][j] !== column[i]) {
                    moved = true;
                }
                grid[i][j] = column[i];
            }
        }
        return moved;
    }

    moveDown(grid) {
        let moved = false;
        for (let j = 0; j < 4; j++) {
            const column = [];
            for (let i = 0; i < 4; i++) {
                if (grid[i][j] !== 0) {
                    column.push(grid[i][j]);
                }
            }
            
            for (let i = column.length - 1; i > 0; i--) {
                if (column[i] === column[i - 1]) {
                    column[i] *= 2;
                    this.score += column[i];
                    column.splice(i - 1, 1);
                    this.createMergeEffect(4 - column.length + i - 1, j, column[i - 1] || column[i]);
                }
            }
            
            while (column.length < 4) {
                column.unshift(0);
            }
            
            for (let i = 0; i < 4; i++) {
                if (grid[i][j] !== column[i]) {
                    moved = true;
                }
                grid[i][j] = column[i];
            }
        }
        return moved;
    }

    createMergeEffect(row, col, value) {
        // 创建合并粒子效果
        const gameGrid = document.getElementById('game-grid');
        const rect = gameGrid.getBoundingClientRect();
        const cellSize = (rect.width - 60) / 4; // 减去padding和gap
        const x = rect.left + 30 + col * (cellSize + 10) + cellSize / 2;
        const y = rect.top + 30 + row * (cellSize + 10) + cellSize / 2;

        for (let i = 0; i < 10; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 10,
                vy: (Math.random() - 0.5) * 10,
                life: 1,
                decay: 0.02,
                color: this.getTileColor(value)
            });
        }
    }

    getTileColor(value) {
        const colors = {
            2: '#ff6b6b',
            4: '#feca57',
            8: '#48dbfb',
            16: '#1dd1a1',
            32: '#a55eea',
            64: '#fd79a8',
            128: '#fdcb6e',
            256: '#6c5ce7',
            512: '#fd79a8',
            1024: '#00b894',
            2048: '#ff7675'
        };
        return colors[value] || '#ffffff';
    }

    updateDisplay() {
        document.getElementById('score').textContent = this.score;
        if (this.score > this.bestScore) {
            this.bestScore = this.score;
            localStorage.setItem('bestScore', this.bestScore);
        }
        document.getElementById('best-score').textContent = this.bestScore;
    }

    render() {
        const gameGrid = document.getElementById('game-grid');
        gameGrid.innerHTML = '';

        // 创建网格单元格
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                const cell = document.createElement('div');
                cell.className = 'grid-cell';
                cell.dataset.row = i;
                cell.dataset.col = j;

                if (this.grid[i][j] !== 0) {
                    const tile = document.createElement('div');
                    tile.className = `tile tile-${this.grid[i][j]}`;
                    tile.textContent = this.grid[i][j];
                    cell.appendChild(tile);
                }

                gameGrid.appendChild(cell);
            }
        }

        this.animateParticles();
    }

    getTileElement(row, col) {
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        return cell ? cell.querySelector('.tile') : null;
    }

    animateParticles() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];

            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay;

            if (particle.life <= 0) {
                this.particles.splice(i, 1);
                continue;
            }

            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        }

        if (this.particles.length > 0) {
            requestAnimationFrame(() => this.animateParticles());
        }
    }

    checkGameState() {
        // 检查是否达到2048
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (this.grid[i][j] === 2048) {
                    this.showOverlay('恭喜！', '你达到了2048！继续游戏获得更高分数！');
                    return;
                }
            }
        }

        // 检查游戏是否结束
        if (this.isGameOver()) {
            this.showOverlay('游戏结束', `最终分数: ${this.score}`);
        }
    }

    isGameOver() {
        // 检查是否有空格
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (this.grid[i][j] === 0) {
                    return false;
                }
            }
        }

        // 检查是否可以合并
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                const current = this.grid[i][j];
                if ((i < 3 && this.grid[i + 1][j] === current) ||
                    (j < 3 && this.grid[i][j + 1] === current)) {
                    return false;
                }
            }
        }

        return true;
    }

    showOverlay(title, message) {
        document.getElementById('overlay-title').textContent = title;
        document.getElementById('overlay-message').textContent = message;
        document.getElementById('game-overlay').classList.add('show');
    }

    hideOverlay() {
        document.getElementById('game-overlay').classList.remove('show');
    }

    toggleAutoPlay() {
        if (this.isAutoPlaying) {
            this.stopAutoPlay();
        } else {
            this.startAutoPlay();
        }
    }

    startAutoPlay() {
        this.isAutoPlaying = true;
        document.getElementById('auto-play-btn').textContent = '停止自动';

        this.autoPlayInterval = setInterval(() => {
            const moves = ['up', 'down', 'left', 'right'];
            const randomMove = moves[Math.floor(Math.random() * moves.length)];
            this.move(randomMove);

            if (this.isGameOver()) {
                this.stopAutoPlay();
            }
        }, 500);
    }

    stopAutoPlay() {
        this.isAutoPlaying = false;
        document.getElementById('auto-play-btn').textContent = '自动游戏';

        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    new Game2048();
});
