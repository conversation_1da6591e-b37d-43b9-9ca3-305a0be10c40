<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 - 五彩斑斓的黑 2048</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #000;
            color: #fff;
            margin: 0;
            padding: 20px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 10px;
            width: 400px;
            height: 400px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px auto;
        }
        .test-cell {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .tile-2 { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .tile-4 { background: linear-gradient(45deg, #feca57, #ff9ff3); }
        .tile-8 { background: linear-gradient(45deg, #48dbfb, #0abde3); }
        .tile-16 { background: linear-gradient(45deg, #1dd1a1, #10ac84); }
        
        .info {
            text-align: center;
            margin: 20px;
        }
        
        button {
            background: #333;
            color: #fff;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>测试页面 - 五彩斑斓的黑 2048</h1>
        <p>这是一个简化的测试版本，用于验证基本功能</p>
        <button onclick="testGame()">测试游戏逻辑</button>
        <button onclick="location.href='index.html'">返回完整版</button>
    </div>
    
    <div class="test-grid" id="test-grid">
        <!-- 测试网格 -->
    </div>
    
    <div class="info">
        <p>分数: <span id="test-score">0</span></p>
        <p>使用方向键移动方块</p>
    </div>

    <script>
        class SimpleGame2048 {
            constructor() {
                this.grid = Array(4).fill().map(() => Array(4).fill(0));
                this.score = 0;
                this.addRandomTile();
                this.addRandomTile();
                this.render();
                this.setupEvents();
            }

            setupEvents() {
                document.addEventListener('keydown', (e) => {
                    switch(e.key) {
                        case 'ArrowUp':
                            e.preventDefault();
                            this.move('up');
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            this.move('down');
                            break;
                        case 'ArrowLeft':
                            e.preventDefault();
                            this.move('left');
                            break;
                        case 'ArrowRight':
                            e.preventDefault();
                            this.move('right');
                            break;
                    }
                });
            }

            addRandomTile() {
                const emptyCells = [];
                for (let i = 0; i < 4; i++) {
                    for (let j = 0; j < 4; j++) {
                        if (this.grid[i][j] === 0) {
                            emptyCells.push({row: i, col: j});
                        }
                    }
                }

                if (emptyCells.length > 0) {
                    const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
                    this.grid[randomCell.row][randomCell.col] = Math.random() < 0.9 ? 2 : 4;
                }
            }

            move(direction) {
                let moved = false;
                const newGrid = this.grid.map(row => [...row]);

                switch(direction) {
                    case 'left':
                        moved = this.moveLeft(newGrid);
                        break;
                    case 'right':
                        moved = this.moveRight(newGrid);
                        break;
                    case 'up':
                        moved = this.moveUp(newGrid);
                        break;
                    case 'down':
                        moved = this.moveDown(newGrid);
                        break;
                }

                if (moved) {
                    this.grid = newGrid;
                    this.addRandomTile();
                    this.render();
                    this.updateScore();
                }
            }

            moveLeft(grid) {
                let moved = false;
                for (let i = 0; i < 4; i++) {
                    const row = grid[i].filter(val => val !== 0);
                    for (let j = 0; j < row.length - 1; j++) {
                        if (row[j] === row[j + 1]) {
                            row[j] *= 2;
                            this.score += row[j];
                            row.splice(j + 1, 1);
                        }
                    }
                    while (row.length < 4) {
                        row.push(0);
                    }
                    
                    for (let j = 0; j < 4; j++) {
                        if (grid[i][j] !== row[j]) {
                            moved = true;
                        }
                    }
                    grid[i] = row;
                }
                return moved;
            }

            moveRight(grid) {
                let moved = false;
                for (let i = 0; i < 4; i++) {
                    const row = grid[i].filter(val => val !== 0);
                    for (let j = row.length - 1; j > 0; j--) {
                        if (row[j] === row[j - 1]) {
                            row[j] *= 2;
                            this.score += row[j];
                            row.splice(j - 1, 1);
                        }
                    }
                    while (row.length < 4) {
                        row.unshift(0);
                    }
                    
                    for (let j = 0; j < 4; j++) {
                        if (grid[i][j] !== row[j]) {
                            moved = true;
                        }
                    }
                    grid[i] = row;
                }
                return moved;
            }

            moveUp(grid) {
                let moved = false;
                for (let j = 0; j < 4; j++) {
                    const column = [];
                    for (let i = 0; i < 4; i++) {
                        if (grid[i][j] !== 0) {
                            column.push(grid[i][j]);
                        }
                    }
                    
                    for (let i = 0; i < column.length - 1; i++) {
                        if (column[i] === column[i + 1]) {
                            column[i] *= 2;
                            this.score += column[i];
                            column.splice(i + 1, 1);
                        }
                    }
                    
                    while (column.length < 4) {
                        column.push(0);
                    }
                    
                    for (let i = 0; i < 4; i++) {
                        if (grid[i][j] !== column[i]) {
                            moved = true;
                        }
                        grid[i][j] = column[i];
                    }
                }
                return moved;
            }

            moveDown(grid) {
                let moved = false;
                for (let j = 0; j < 4; j++) {
                    const column = [];
                    for (let i = 0; i < 4; i++) {
                        if (grid[i][j] !== 0) {
                            column.push(grid[i][j]);
                        }
                    }
                    
                    for (let i = column.length - 1; i > 0; i--) {
                        if (column[i] === column[i - 1]) {
                            column[i] *= 2;
                            this.score += column[i];
                            column.splice(i - 1, 1);
                        }
                    }
                    
                    while (column.length < 4) {
                        column.unshift(0);
                    }
                    
                    for (let i = 0; i < 4; i++) {
                        if (grid[i][j] !== column[i]) {
                            moved = true;
                        }
                        grid[i][j] = column[i];
                    }
                }
                return moved;
            }

            render() {
                const testGrid = document.getElementById('test-grid');
                testGrid.innerHTML = '';

                for (let i = 0; i < 4; i++) {
                    for (let j = 0; j < 4; j++) {
                        const cell = document.createElement('div');
                        cell.className = 'test-cell';
                        
                        if (this.grid[i][j] !== 0) {
                            cell.textContent = this.grid[i][j];
                            cell.classList.add(`tile-${this.grid[i][j]}`);
                        }
                        
                        testGrid.appendChild(cell);
                    }
                }
            }

            updateScore() {
                document.getElementById('test-score').textContent = this.score;
            }
        }

        let game;

        function testGame() {
            game = new SimpleGame2048();
            console.log('游戏已初始化');
        }

        // 页面加载时自动开始测试
        document.addEventListener('DOMContentLoaded', () => {
            testGame();
        });
    </script>
</body>
</html>
